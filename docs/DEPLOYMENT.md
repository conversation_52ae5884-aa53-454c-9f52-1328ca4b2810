# 肺功能数据管理平台 - 部署指南

## 概述

本文档详细说明如何在不同环境中部署肺功能数据管理平台。

## 系统要求

### 最低配置

- **CPU**: 2核心
- **内存**: 4GB RAM
- **磁盘**: 20GB 可用空间
- **操作系统**: Linux (推荐 Ubuntu 20.04+)

### 推荐配置

- **CPU**: 4核心
- **内存**: 8GB RAM
- **磁盘**: 50GB SSD
- **操作系统**: Ubuntu 22.04 LTS

### 软件依赖

- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+ (本地开发)

## 快速部署 (Docker Compose)

### 1. 克隆项目

```bash
git clone <repository-url>
cd free_lung_function_project_admin
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env.production

# 编辑生产环境配置
nano .env.production
```

必须配置的环境变量:

```env
# 应用配置
NODE_ENV=production
PORT=3000
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your-very-secure-secret-key-here

# 数据库配置 (腾讯云)
DATABASE_URL="mysql://username:password@host:port/database"

# 或使用本地数据库
DATABASE_URL="mysql://admin:admin123@mysql:3306/lung_function_db"
```

### 3. 执行部署

```bash
# 使用部署脚本 (推荐)
./scripts/deploy.sh

# 或手动执行
docker-compose up -d
```

### 4. 初始化数据库

```bash
# 运行数据库迁移
docker-compose exec app npx prisma db push

# 创建管理员用户 (如果需要)
docker-compose exec app node -e "
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
async function createAdmin() {
  const hash = await bcrypt.hash('admin123', 12);
  await prisma.user.create({
    data: {
      username: 'admin',
      nickname: '系统管理员',
      passwordHash: hash,
      role: 'admin',
      isActive: true,
    }
  });
  console.log('管理员用户创建成功');
  await prisma.\$disconnect();
}
createAdmin();
"
```

### 5. 验证部署

```bash
# 检查服务状态
docker-compose ps

# 查看应用日志
docker-compose logs -f app

# 健康检查
curl http://localhost:3000/api/health
```

## 生产环境部署

### 腾讯云服务器部署

#### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 重新登录以应用 Docker 组权限
logout
```

#### 2. 防火墙配置

```bash
# 开放必要端口
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

#### 3. SSL 证书配置 (HTTPS)

```bash
# 安装 Certbot
sudo apt install certbot

# 获取 SSL 证书
sudo certbot certonly --standalone -d yourdomain.com

# 复制证书到项目目录
sudo mkdir -p docker/nginx/ssl
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem docker/nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem docker/nginx/ssl/key.pem
sudo chown -R $USER:$USER docker/nginx/ssl
```

#### 4. 生产环境配置

编辑 `docker-compose.yml`，启用生产配置:

```yaml
# 取消注释 nginx 服务的 profiles 配置
# profiles:
#   - production
```

启动生产环境:

```bash
docker-compose --profile production up -d
```

### 数据库配置

#### 腾讯云数据库配置

1. 登录腾讯云控制台
2. 创建 MySQL 实例
3. 配置安全组，允许应用服务器访问
4. 创建数据库和用户
5. 更新 `.env.production` 中的 `DATABASE_URL`

#### 本地数据库配置

如果使用本地数据库，确保 MySQL 服务正常运行:

```bash
# 检查 MySQL 服务
docker-compose exec mysql mysqladmin ping

# 查看数据库日志
docker-compose logs mysql
```

## 域名和SSL配置

### 1. 域名解析

将域名 A 记录指向服务器 IP:

```
A    @    your-server-ip
A    www  your-server-ip
```

### 2. Nginx 配置

编辑 `docker/nginx/nginx.conf`，更新域名:

```nginx
server_name yourdomain.com www.yourdomain.com;
```

### 3. SSL 自动续期

```bash
# 添加自动续期任务
sudo crontab -e

# 添加以下行 (每月1日凌晨2点执行)
0 2 1 * * /usr/bin/certbot renew --quiet && docker-compose restart nginx
```

## 监控和维护

### 日志管理

```bash
# 查看应用日志
docker-compose logs -f app

# 查看 Nginx 日志
docker-compose logs -f nginx

# 查看系统资源使用
docker stats

# 清理日志文件
docker-compose exec app sh -c "truncate -s 0 /app/logs/*.log"
```

### 备份

```bash
# 数据库备份脚本
#!/bin/bash
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/backup"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
docker-compose exec mysql mysqldump -u root -p$DB_ROOT_PASSWORD lung_function_db > $BACKUP_DIR/db_backup_$DATE.sql

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz public/uploads/

# 删除30天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "备份完成: $DATE"
```

### 性能优化

```bash
# 查看容器资源使用
docker stats --no-stream

# 优化 Docker 镜像大小
docker system prune -f

# 监控磁盘使用
df -h

# 监控内存使用
free -h
```

## 故障排除

### 常见问题

#### 1. 容器启动失败

```bash
# 查看详细错误
docker-compose logs app

# 检查配置文件
docker-compose config

# 重新构建镜像
docker-compose build --no-cache app
```

#### 2. 数据库连接失败

```bash
# 检查数据库状态
docker-compose exec mysql mysqladmin ping

# 检查网络连接
docker-compose exec app ping mysql

# 验证环境变量
docker-compose exec app env | grep DATABASE_URL
```

#### 3. 文件上传失败

```bash
# 检查目录权限
ls -la public/uploads/

# 修复权限
sudo chown -R 1001:1001 public/uploads/
```

#### 4. SSL 证书问题

```bash
# 检查证书有效期
openssl x509 -in docker/nginx/ssl/cert.pem -text -noout | grep "Not After"

# 测试 SSL 配置
docker-compose exec nginx nginx -t

# 重新加载 Nginx 配置
docker-compose exec nginx nginx -s reload
```

### 服务重启

```bash
# 重启单个服务
docker-compose restart app

# 重启所有服务
docker-compose restart

# 完全重新部署
docker-compose down
docker-compose up -d
```

## 安全建议

### 1. 系统安全

- 定期更新系统和 Docker
- 配置防火墙规则
- 禁用不必要的服务
- 使用非 root 用户运行应用

### 2. 应用安全

- 使用强密码和复杂的 Secret
- 定期更换密钥
- 启用 HTTPS
- 配置安全响应头

### 3. 数据安全

- 定期备份数据库
- 加密敏感数据
- 限制数据库访问权限
- 监控异常访问

## 扩展和升级

### 水平扩展

```yaml
# docker-compose.yml
services:
  app:
    deploy:
      replicas: 3
    # 添加负载均衡器
  nginx:
    # 配置负载均衡
```

### 升级流程

```bash
# 1. 备份数据
./scripts/backup.sh

# 2. 停止服务
docker-compose down

# 3. 拉取新代码
git pull origin main

# 4. 重新构建
docker-compose build --no-cache

# 5. 启动服务
docker-compose up -d

# 6. 验证升级
curl http://localhost:3000/api/health
```

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看日志文件
2. 检查配置文件
3. 参考故障排除部分
4. 联系技术支持

部署成功后，可以通过以下方式访问系统：

- **Web界面**: https://yourdomain.com
- **管理员账户**: admin / admin123
- **健康检查**: https://yourdomain.com/api/health
