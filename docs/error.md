MacBook-Pro-6:free_lung_function_project_admin macbookair$ npm run build

> lung-function-admin@0.1.0 build
> next build

  ▲ Next.js 14.2.5
  - Environments: .env.local, .env.production, .env
  - Experiments (use with caution):
    · scrollRestoration

   Creating an optimized production build ...
 ⚠ Found lockfile missing swc dependencies, patching...
 ⨯ Failed to patch lockfile, please try uninstalling and reinstalling next in this workspace
TypeError: fetch failed
    at node:internal/deps/undici/undici:13392:13
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async fetchPkgInfo (/Users/<USER>/PythonProjects/free_lung_function_project_admin/node_modules/next/dist/lib/patch-incorrect-lockfile.js:66:17)
    at async Promise.all (index 5)
    at async patchIncorrectLockfile (/Users/<USER>/PythonProjects/free_lung_function_project_admin/node_modules/next/dist/lib/patch-incorrect-lockfile.js:162:26) {
  [cause]: Error: certificate has expired
      at TLSSocket.onConnectSecure (node:_tls_wrap:1677:34)
      at TLSSocket.emit (node:events:518:28)
      at TLSSocket._finishInit (node:_tls_wrap:1076:8)
      at ssl.onhandshakedone (node:_tls_wrap:862:12) {
    code: 'CERT_HAS_EXPIRED'
  }
}
 ✓ Compiled successfully
   Linting and checking validity of types  .. ⨯ ESLint: Failed to load config "@typescript-eslint/recommended" to extend from. Referenced from: /Users/<USER>/PythonProjects/free_lung_function_project_admin/.eslintrc.js
 ✓ Linting and checking validity of types    
 ✓ Collecting page data    
   Generating static pages (0/25)  [    ] ⚠ Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /login. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /data/view. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /data/view. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /data/export. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /data/export. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /data/batch. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /data/batch. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /settings/profile. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /settings/profile. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
   Generating static pages (12/25)  [=   ] ⚠ Unsupported metadata viewport is configured in metadata export in /help. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /help. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /settings/logs. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /settings/logs. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /settings/users. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /settings/users. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /forms. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /forms. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /forms/list. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /forms/list. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /settings/password. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /settings/password. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /forms/config. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /forms/config. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /_not-found. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /_not-found. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /validation-failures. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
 ⚠ Unsupported metadata viewport is configured in metadata export in /validation-failures. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
prisma:query SELECT 1
 ✓ Generating static pages (25/25)
 ✓ Collecting build traces    
 ⚠ Failed to copy traced files for /Users/<USER>/PythonProjects/free_lung_function_project_admin/.next/server/app/(dashboard)/page.js Error: ENOENT: no such file or directory, copyfile '/Users/<USER>/PythonProjects/free_lung_function_project_admin/.next/server/app/(dashboard)/page_client-reference-manifest.js' -> '/Users/<USER>/PythonProjects/free_lung_function_project_admin/.next/standalone/.next/server/app/(dashboard)/page_client-reference-manifest.js'
    at async Object.copyFile (node:internal/fs/promises:623:10)
    at async /Users/<USER>/PythonProjects/free_lung_function_project_admin/node_modules/next/dist/build/utils.js:1406:21
    at async Promise.all (index 17)
    at async handleTraceFiles (/Users/<USER>/PythonProjects/free_lung_function_project_admin/node_modules/next/dist/build/utils.js:1387:9)
    at async copyTracedFiles (/Users/<USER>/PythonProjects/free_lung_function_project_admin/node_modules/next/dist/build/utils.js:1461:13)
    at async /Users/<USER>/PythonProjects/free_lung_function_project_admin/node_modules/next/dist/build/index.js:225:9
    at async Span.traceAsyncFn (/Users/<USER>/PythonProjects/free_lung_function_project_admin/node_modules/next/dist/trace/trace.js:154:20)
    at async writeStandaloneDirectory (/Users/<USER>/PythonProjects/free_lung_function_project_admin/node_modules/next/dist/build/index.js:224:5)
    at async /Users/<USER>/PythonProjects/free_lung_function_project_admin/node_modules/next/dist/build/index.js:2136:17
    at async Span.traceAsyncFn (/Users/<USER>/PythonProjects/free_lung_function_project_admin/node_modules/next/dist/trace/trace.js:154:20) {
  errno: -2,
  code: 'ENOENT',
  syscall: 'copyfile',
  path: '/Users/<USER>/PythonProjects/free_lung_function_project_admin/.next/server/app/(dashboard)/page_client-reference-manifest.js',
  dest: '/Users/<USER>/PythonProjects/free_lung_function_project_admin/.next/standalone/.next/server/app/(dashboard)/page_client-reference-manifest.js'
}
 ✓ Finalizing page optimization    

Route (app)                             Size     First Load JS
┌ ○ /                                   139 B           540 kB
├ ○ /_not-found                         187 B           540 kB
├ ƒ /api/auth/[...nextauth]             0 B                0 B
├ ƒ /api/dashboard                      0 B                0 B
├ ƒ /api/data/[formId]                  0 B                0 B
├ ƒ /api/data/[formId]/batch            0 B                0 B
├ ƒ /api/data/[formId]/export           0 B                0 B
├ ƒ /api/data/batch/errors              0 B                0 B
├ ƒ /api/data/batch/import              0 B                0 B
├ ƒ /api/data/batch/template            0 B                0 B
├ ƒ /api/forms                          0 B                0 B
├ ƒ /api/forms/[id]                     0 B                0 B
├ ○ /api/health                         0 B                0 B
├ ƒ /api/logs                           0 B                0 B
├ ○ /api/test                           0 B                0 B
├ ƒ /api/user/password                  0 B                0 B
├ ƒ /api/user/profile                   0 B                0 B
├ ƒ /api/users                          0 B                0 B
├ ƒ /api/users/[id]                     0 B                0 B
├ ƒ /api/users/[id]/reset-password      0 B                0 B
├ ƒ /api/validation-failures            0 B                0 B
├ ƒ /api/validation-failures/stats      0 B                0 B
├ ƒ /api/webhook/[formId]               0 B                0 B
├ ○ /dashboard                          3.02 kB         542 kB
├ ○ /data/batch                         2.84 kB         542 kB
├ ○ /data/export                        2.97 kB         542 kB
├ ○ /data/view                          3.89 kB         543 kB
├ ○ /forms                              2.73 kB         542 kB
├ ○ /forms/config                       6.89 kB         546 kB
├ ○ /forms/list                         1.69 kB         541 kB
├ ○ /help                               2.89 kB         542 kB
├ ○ /login                              1.48 kB         541 kB
├ ○ /settings/logs                      3.17 kB         543 kB
├ ○ /settings/password                  2.17 kB         542 kB
├ ○ /settings/profile                   2.3 kB          542 kB
├ ○ /settings/users                     3.48 kB         543 kB
└ ○ /validation-failures                4.53 kB         544 kB
+ First Load JS shared by all           539 kB
  └ chunks/vendors-458da18dfe75bd9d.js  537 kB
  └ other shared chunks (total)         1.96 kB


○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand